'use client';

import { TonCon<PERSON>UIProvider } from '@tonconnect/ui-react';
import { Toaster } from 'sonner';

import { RootLayoutHeader } from '@/app/(app)/root-layout-header';
import { TelegramProvider } from '@/components/TelegramProvider';
import { RootProvider } from '@/root-context';
import { WALLET_MANIFEST_URL } from '@/utils/ton-constants';

import RootLayoutFooter from './root-layout-footer';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <RootProvider>
      <TelegramProvider>
        <TonConnectUIProvider manifestUrl={WALLET_MANIFEST_URL}>
          <RootLayoutHeader />
          <div className="min-h-screen flex flex-col pt-[72px] pb-16">
            <main className="flex-1 p-2">
              <div className="max-w-6xl mx-auto">{children}</div>
            </main>
          </div>
          <Toaster
            theme="dark"
            toastOptions={{
              style: {
                background: '#232e3c',
                border: '1px solid #3a4a5c',
                color: '#f5f5f5',
              },
            }}
          />
          <RootLayoutFooter />
        </TonConnectUIProvider>
      </TelegramProvider>
    </RootProvider>
  );
}
