import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import { AppCloudFunctions, type OrderEntity } from '@/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

const COLLECTION_NAME = 'orders';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export const getSecondaryMarketOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    let q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', '==', 'paid'),
      where('secondaryMarketPrice', '>', 0),
    );

    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy('secondaryMarketPrice', 'asc'));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy('secondaryMarketPrice', 'desc'));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (const element of docs) {
      const doc = element;

      if (processedCount >= pageSize) {
        hasMore = true;
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      if (
        filters.currentUserId &&
        (orderData.buyerId === filters.currentUserId ||
          orderData.sellerId === filters.currentUserId)
      ) {
        orders.push(orderData);
        lastDoc = doc;
        processedCount++;
      }
    }

    if (filters.minPrice !== undefined) {
      orders = orders.filter(
        (order) => (order.secondaryMarketPrice ?? 0) >= filters.minPrice!,
      );
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter(
        (order) => (order.secondaryMarketPrice ?? 0) <= filters.maxPrice!,
      );
    }

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    return result;
  } catch (error) {
    console.error('Error fetching secondary market orders:', error);
    throw error;
  }
};

export const makeSecondaryMarketPurchase = async (orderId: string) => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makeSecondaryMarketPurchase);

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    return result.data;
  } catch (error) {
    console.error('Error making secondary market purchase:', error);
    throw error;
  }
};
