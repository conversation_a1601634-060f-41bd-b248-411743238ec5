import { getDocs } from 'firebase/firestore';
import {
  type OrderFilters,
  type PaginatedOrdersResult,
  createSecondaryMarketQuery,
  createPaginationConfig,
  processSecondaryMarketPagination,
  createPaginatedResult,
  createPriceFilter,
  makeSecondaryMarketPurchase as callMakeSecondaryMarketPurchase,
  ERROR_MESSAGES,
} from './shared';

export const getSecondaryMarketOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const paginationConfig = createPaginationConfig(filters);
    const query = createSecondaryMarketQuery(filters);

    const snapshot = await getDocs(query);

    // Process pagination with user filtering if needed
    const paginationResult = processSecondaryMarketPagination(
      snapshot,
      paginationConfig.pageSize,
      filters.currentUserId,
    );

    // Apply client-side price filtering if needed
    let filteredOrders = paginationResult.items;

    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      const priceFilter = createPriceFilter(filters.minPrice, filters.maxPrice);
      filteredOrders = filteredOrders.filter(priceFilter);
    }

    return createPaginatedResult({
      ...paginationResult,
      items: filteredOrders,
    });
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_SECONDARY_MARKET, error);
    throw error;
  }
};

// Re-export types from shared module for backward compatibility
export type { MakeSecondaryMarketPurchaseResponse } from './shared';

// Use the declarative cloud function caller
export const makeSecondaryMarketPurchase = callMakeSecondaryMarketPurchase;
