import {
  getCountFromServer,
  getDocs,
  query,
  where,
  collection,
} from 'firebase/firestore';
import type { OrderEntity } from '@/core.constants';
import { ORDERS_COLLECTION_NAME } from '@/core.constants';
import { firestore } from '@/root-context';
import {
  type OrderFilters,
  type PaginatedOrdersResult,
  type OrderStats,
  createUserOrdersQuery,
  createOrderStatsQueries,
  removeDuplicateOrders,
  filterAndSortOrders,
  hasSecondaryMarketPrice,
  setSecondaryMarketPrice as callSetSecondaryMarketPrice,
  cancelOrder as callCancelOrder,
  ERROR_MESSAGES,
  API_CONSTANTS,
} from './shared';

export const getPaidOrdersCount = async (): Promise<number> => {
  try {
    const q = query(
      collection(firestore, API_CONSTANTS.COLLECTIONS.ORDERS),
      where('status', '==', 'paid'),
    );

    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_PAID_ORDERS_COUNT, error);
    throw error;
  }
};

export const getOrderStats = async (): Promise<OrderStats> => {
  try {
    const queries = createOrderStatsQueries();

    const [
      totalOrdersSnapshot,
      giftSentToRelayerSnapshot,
      fulfilledOrdersSnapshot,
      cancelledOrdersSnapshot,
      paidOrdersSnapshot,
    ] = await Promise.all([
      getCountFromServer(queries.total),
      getCountFromServer(queries.giftSentToRelayer),
      getCountFromServer(queries.fulfilled),
      getCountFromServer(queries.cancelled),
      getDocs(queries.paid),
    ]);

    // Count paid orders with and without secondary market price
    let paidOrdersWithoutSecondaryPrice = 0;
    let paidOrdersWithSecondaryPrice = 0;

    paidOrdersSnapshot.docs.forEach((doc) => {
      const orderData = doc.data() as OrderEntity;
      if (hasSecondaryMarketPrice(orderData)) {
        paidOrdersWithSecondaryPrice++;
      } else {
        paidOrdersWithoutSecondaryPrice++;
      }
    });

    const stats: OrderStats = {
      totalOrders: totalOrdersSnapshot.data().count,
      paidOrdersWithoutSecondaryPrice,
      paidOrdersWithSecondaryPrice,
      giftSentToRelayerOrders: giftSentToRelayerSnapshot.data().count,
      fulfilledOrders: fulfilledOrdersSnapshot.data().count,
      cancelledOrders: cancelledOrdersSnapshot.data().count,
    };

    return stats;
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_ORDER_STATS, error);
    throw error;
  }
};

export const getUserOrders = async (userId: string): Promise<OrderEntity[]> => {
  try {
    const buyerOrdersQuery = createUserOrdersQuery(userId, 'buyer');
    const sellerOrdersQuery = createUserOrdersQuery(userId, 'seller');

    const [buyerSnapshot, sellerSnapshot] = await Promise.all([
      getDocs(buyerOrdersQuery),
      getDocs(sellerOrdersQuery),
    ]);

    const orders: OrderEntity[] = [];

    buyerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    sellerSnapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    // Remove duplicates and apply sorting
    const uniqueOrders = removeDuplicateOrders(orders);
    const sortedOrders = filterAndSortOrders(uniqueOrders);

    return sortedOrders;
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_USER_ORDERS, error);
    throw error;
  }
};

// Re-export types from shared module for backward compatibility
export type {
  SetSecondaryMarketPriceResponse,
  CancelOrderResponse,
} from './shared';

// Use the declarative cloud function callers
export const setSecondaryMarketPrice = callSetSecondaryMarketPrice;
export const cancelOrder = callCancelOrder;
