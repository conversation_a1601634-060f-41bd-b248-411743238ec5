import type { OrderEntity } from '@/core.constants';

import { ORDER_STATUS_PRIORITY } from './constants';
import type { OrderFilters } from './types';

export type OrderPredicate = (order: OrderEntity) => boolean;
export type OrderComparator = (a: OrderEntity, b: OrderEntity) => number;

// Price filtering predicates
export const createPriceFilter = (
  minPrice?: number,
  maxPrice?: number,
): OrderPredicate => {
  return (order: OrderEntity) => {
    const price = order.secondaryMarketPrice ?? 0;

    if (minPrice !== undefined && price < minPrice) {
      return false;
    }

    if (maxPrice !== undefined && price > maxPrice) {
      return false;
    }

    return true;
  };
};

// User filtering predicates
export const createUserFilter = (
  userId: string,
  role?: 'buyer' | 'seller',
): OrderPredicate => {
  return (order: OrderEntity) => {
    if (!role) {
      return order.buyerId === userId || order.sellerId === userId;
    }

    return role === 'buyer'
      ? order.buyerId === userId
      : order.sellerId === userId;
  };
};

// Status filtering predicates
export const createStatusFilter = (status: string): OrderPredicate => {
  return (order: OrderEntity) => order.status === status;
};

// Collection filtering predicates
export const createCollectionFilter = (
  collectionId: string,
): OrderPredicate => {
  return (order: OrderEntity) => order.collectionId === collectionId;
};

// Secondary market filtering predicates
export const createSecondaryMarketFilter = (): OrderPredicate => {
  return (order: OrderEntity) => {
    return (
      order.status === 'paid' &&
      order.secondaryMarketPrice !== null &&
      order.secondaryMarketPrice !== undefined &&
      order.secondaryMarketPrice > 0
    );
  };
};

// Composite filter creator
export const createCompositeFilter = (
  filters: OrderFilters,
): OrderPredicate => {
  const predicates: OrderPredicate[] = [];

  if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
    predicates.push(createPriceFilter(filters.minPrice, filters.maxPrice));
  }

  if (filters.currentUserId) {
    predicates.push(createUserFilter(filters.currentUserId));
  }

  if (filters.collectionId) {
    predicates.push(createCollectionFilter(filters.collectionId));
  }

  return (order: OrderEntity) =>
    predicates.every((predicate) => predicate(order));
};

// Sorting comparators
export const createStatusComparator = (): OrderComparator => {
  return (a: OrderEntity, b: OrderEntity) => {
    const aPriority =
      ORDER_STATUS_PRIORITY[a.status as keyof typeof ORDER_STATUS_PRIORITY] ||
      999;
    const bPriority =
      ORDER_STATUS_PRIORITY[b.status as keyof typeof ORDER_STATUS_PRIORITY] ||
      999;

    return aPriority - bPriority;
  };
};

export const createDateComparator = (
  direction: 'asc' | 'desc' = 'desc',
): OrderComparator => {
  return (a: OrderEntity, b: OrderEntity) => {
    const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
    const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;

    return direction === 'desc' ? bDate - aDate : aDate - bDate;
  };
};

export const createPriceComparator = (
  direction: 'asc' | 'desc' = 'desc',
): OrderComparator => {
  return (a: OrderEntity, b: OrderEntity) => {
    const aPrice = a.secondaryMarketPrice ?? 0;
    const bPrice = b.secondaryMarketPrice ?? 0;

    return direction === 'desc' ? bPrice - aPrice : aPrice - bPrice;
  };
};

// Composite sorting
export const createCompositeComparator = (
  comparators: OrderComparator[],
): OrderComparator => {
  return (a: OrderEntity, b: OrderEntity) => {
    for (const comparator of comparators) {
      const result = comparator(a, b);
      if (result !== 0) {
        return result;
      }
    }
    return 0;
  };
};

// Default user orders sorting (status priority + date)
export const createUserOrdersComparator = (): OrderComparator => {
  return createCompositeComparator([
    createStatusComparator(),
    createDateComparator('desc'),
  ]);
};

// Filter and sort orders
export const filterAndSortOrders = (
  orders: OrderEntity[],
  filters: OrderFilters = {},
): OrderEntity[] => {
  let result = [...orders];

  // Apply filters
  const compositeFilter = createCompositeFilter(filters);
  result = result.filter(compositeFilter);

  // Apply sorting based on sortBy parameter
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'price_asc':
        result.sort(createPriceComparator('asc'));
        break;
      case 'price_desc':
        result.sort(createPriceComparator('desc'));
        break;
      case 'date_asc':
        result.sort(createDateComparator('asc'));
        break;
      case 'date_desc':
        result.sort(createDateComparator('desc'));
        break;
    }
  } else {
    // Default sorting for user orders
    result.sort(createUserOrdersComparator());
  }

  return result;
};

// Remove duplicate orders by ID
export const removeDuplicateOrders = (orders: OrderEntity[]): OrderEntity[] => {
  return orders.filter(
    (order, index, self) => index === self.findIndex((o) => o.id === order.id),
  );
};

// Check if order has secondary market price
export const hasSecondaryMarketPrice = (order: OrderEntity): boolean => {
  return (
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};
