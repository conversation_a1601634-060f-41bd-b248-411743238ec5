import { httpsCallable, type HttpsCallableResult } from 'firebase/functions';

import { AppCloudFunctions } from '@/core.constants';
import { firebaseFunctions } from '@/root-context';

import { ERROR_MESSAGES } from './constants';
import type {
  ApiResponse,
  CancelOrderResponse,
  CloudFunctionCall,
  MakeSecondaryMarketPurchaseResponse,
  SetSecondaryMarketPriceResponse,
} from './types';

export class CloudFunctionCaller {
  private static instance: CloudFunctionCaller;

  static getInstance(): CloudFunctionCaller {
    if (!CloudFunctionCaller.instance) {
      CloudFunctionCaller.instance = new CloudFunctionCaller();
    }
    return CloudFunctionCaller.instance;
  }

  private async callFunction<TRequest, TResponse>(
    functionName: string,
    data: TRequest,
    errorMessage: string,
  ): Promise<TResponse> {
    try {
      const cloudFunction = httpsCallable<TRequest, TResponse>(
        firebaseFunctions,
        functionName,
      );

      const result: HttpsCallableResult<TResponse> = await cloudFunction(data);
      return result.data;
    } catch (error) {
      console.error(errorMessage, error);
      throw error;
    }
  }

  async setSecondaryMarketPrice(
    orderId: string,
    secondaryMarketPrice: number,
  ): Promise<SetSecondaryMarketPriceResponse> {
    return this.callFunction(
      AppCloudFunctions.setSecondaryMarketPrice,
      { orderId, secondaryMarketPrice },
      ERROR_MESSAGES.SET_SECONDARY_PRICE,
    );
  }

  async cancelOrder(
    orderId: string,
    userId: string,
  ): Promise<CancelOrderResponse> {
    return this.callFunction(
      AppCloudFunctions.cancelOrder,
      { orderId, userId },
      ERROR_MESSAGES.CANCEL_ORDER,
    );
  }

  async makeSecondaryMarketPurchase(
    orderId: string,
  ): Promise<MakeSecondaryMarketPurchaseResponse> {
    return this.callFunction(
      AppCloudFunctions.makeSecondaryMarketPurchase,
      { orderId },
      ERROR_MESSAGES.MAKE_SECONDARY_PURCHASE,
    );
  }

  async makePurchaseAsBuyer(data: any): Promise<any> {
    return this.callFunction(
      AppCloudFunctions.makePurchaseAsBuyer,
      data,
      'Error making purchase as buyer',
    );
  }

  async makePurchaseAsSeller(data: any): Promise<any> {
    return this.callFunction(
      AppCloudFunctions.makePurchaseAsSeller,
      data,
      'Error making purchase as seller',
    );
  }

  async withdrawRevenue(data: any): Promise<any> {
    return this.callFunction(
      AppCloudFunctions.withdrawRevenue,
      data,
      'Error withdrawing revenue',
    );
  }

  async changeUserData(data: any): Promise<any> {
    return this.callFunction(
      AppCloudFunctions.changeUserData,
      data,
      'Error changing user data',
    );
  }
}

// Singleton instance
export const cloudFunctionCaller = CloudFunctionCaller.getInstance();

// Declarative function call configurations
export const CLOUD_FUNCTION_CONFIGS = {
  setSecondaryMarketPrice: {
    functionName: AppCloudFunctions.setSecondaryMarketPrice,
    errorMessage: ERROR_MESSAGES.SET_SECONDARY_PRICE,
  },
  cancelOrder: {
    functionName: AppCloudFunctions.cancelOrder,
    errorMessage: ERROR_MESSAGES.CANCEL_ORDER,
  },
  makeSecondaryMarketPurchase: {
    functionName: AppCloudFunctions.makeSecondaryMarketPurchase,
    errorMessage: ERROR_MESSAGES.MAKE_SECONDARY_PURCHASE,
  },
  makePurchaseAsBuyer: {
    functionName: AppCloudFunctions.makePurchaseAsBuyer,
    errorMessage: 'Error making purchase as buyer',
  },
  makePurchaseAsSeller: {
    functionName: AppCloudFunctions.makePurchaseAsSeller,
    errorMessage: 'Error making purchase as seller',
  },
  withdrawRevenue: {
    functionName: AppCloudFunctions.withdrawRevenue,
    errorMessage: 'Error withdrawing revenue',
  },
  changeUserData: {
    functionName: AppCloudFunctions.changeUserData,
    errorMessage: 'Error changing user data',
  },
} as const;

// Generic cloud function caller
export const callCloudFunction = async <TRequest, TResponse>(
  config: CloudFunctionCall<TRequest, TResponse>,
): Promise<TResponse> => {
  try {
    const cloudFunction = httpsCallable<TRequest, TResponse>(
      firebaseFunctions,
      config.functionName,
    );

    const result = await cloudFunction(config.data);
    return result.data;
  } catch (error) {
    console.error(`Error calling ${config.functionName}:`, error);
    throw error;
  }
};

// Convenience functions using the singleton
export const setSecondaryMarketPrice = (
  orderId: string,
  secondaryMarketPrice: number,
): Promise<SetSecondaryMarketPriceResponse> => {
  return cloudFunctionCaller.setSecondaryMarketPrice(
    orderId,
    secondaryMarketPrice,
  );
};

export const cancelOrder = (
  orderId: string,
  userId: string,
): Promise<CancelOrderResponse> => {
  return cloudFunctionCaller.cancelOrder(orderId, userId);
};

export const makeSecondaryMarketPurchase = (
  orderId: string,
): Promise<MakeSecondaryMarketPurchaseResponse> => {
  return cloudFunctionCaller.makeSecondaryMarketPurchase(orderId);
};
