import type { DocumentSnapshot } from 'firebase/firestore';

import type { OrderEntity } from '@/core.constants';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface OrderStats {
  totalOrders: number;
  paidOrdersWithoutSecondaryPrice: number;
  paidOrdersWithSecondaryPrice: number;
  giftSentToRelayerOrders: number;
  fulfilledOrders: number;
  cancelledOrders: number;
}

export interface SetSecondaryMarketPriceResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface CancelOrderResponse {
  success: boolean;
  message: string;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export type SortDirection = 'asc' | 'desc';
export type SortField = 'price' | 'date' | 'createdAt' | 'secondaryMarketPrice';

export interface QueryConfig {
  collection: string;
  filters: Array<{
    field: string;
    operator: '==' | '!=' | '>' | '<' | '>=' | '<=' | 'in' | 'array-contains';
    value: any;
  }>;
  orderBy?: Array<{
    field: string;
    direction: SortDirection;
  }>;
  limit?: number;
  startAfter?: DocumentSnapshot;
}

export interface CloudFunctionCall<TRequest = any, TResponse = any> {
  functionName: string;
  data: TRequest;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}
