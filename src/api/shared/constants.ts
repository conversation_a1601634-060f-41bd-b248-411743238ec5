import { ORDERS_COLLECTION_NAME } from '@/core.constants';

export const API_CONSTANTS = {
  COLLECTIONS: {
    ORDERS: ORDERS_COLLECTION_NAME,
  },
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
  },
  SORT: {
    DEFAULT_DIRECTION: 'desc' as const,
    DEFAULT_FIELD: 'createdAt' as const,
  },
  FILTERS: {
    SECONDARY_MARKET: {
      STATUS: 'paid',
      MIN_PRICE: 0,
    },
  },
} as const;

export const ORDER_STATUS_PRIORITY = {
  gift_sent_to_relayer: 1,
  paid: 2,
  active: 3,
  cancelled: 4,
  fulfilled: 5,
} as const;

export const SORT_FIELD_MAPPING = {
  price_asc: { field: 'secondaryMarketPrice', direction: 'asc' },
  price_desc: { field: 'secondaryMarketPrice', direction: 'desc' },
  date_asc: { field: 'createdAt', direction: 'asc' },
  date_desc: { field: 'createdAt', direction: 'desc' },
} as const;

export const ERROR_MESSAGES = {
  FETCH_ORDERS: 'Error fetching orders',
  FETCH_USER_ORDERS: 'Error fetching user orders',
  FETCH_SECONDARY_MARKET: 'Error fetching secondary market orders',
  FETCH_ORDER_STATS: 'Error getting order stats',
  FETCH_PAID_ORDERS_COUNT: 'Error getting paid orders count',
  SET_SECONDARY_PRICE: 'Error setting secondary market price',
  CANCEL_ORDER: 'Error cancelling order',
  MAKE_SECONDARY_PURCHASE: 'Error making secondary market purchase',
} as const;
