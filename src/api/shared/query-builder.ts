import {
  collection,
  type DocumentSnapshot,
  limit,
  orderBy,
  type Query,
  query,
  startAfter,
  where,
} from 'firebase/firestore';

import { firestore } from '@/root-context';

import { API_CONSTANTS, SORT_FIELD_MAPPING } from './constants';
import type { OrderFilters, QueryConfig, SortDirection } from './types';

export class QueryBuilder {
  private config: QueryConfig;

  constructor(collectionName: string) {
    this.config = {
      collection: collectionName,
      filters: [],
    };
  }

  static forCollection(collectionName: string): QueryBuilder {
    return new QueryBuilder(collectionName);
  }

  addFilter(
    field: string,
    operator: QueryConfig['filters'][0]['operator'],
    value: any,
  ): this {
    if (value !== undefined && value !== null) {
      this.config.filters.push({ field, operator, value });
    }
    return this;
  }

  addOrderBy(field: string, direction: SortDirection = 'desc'): this {
    if (!this.config.orderBy) {
      this.config.orderBy = [];
    }
    this.config.orderBy.push({ field, direction });
    return this;
  }

  setLimit(limitValue: number): this {
    this.config.limit = Math.min(
      limitValue,
      API_CONSTANTS.PAGINATION.MAX_PAGE_SIZE,
    );
    return this;
  }

  setStartAfter(doc: DocumentSnapshot): this {
    this.config.startAfter = doc;
    return this;
  }

  build(): Query {
    let q = collection(firestore, this.config.collection);

    // Apply filters
    for (const filter of this.config.filters) {
      q = query(q, where(filter.field, filter.operator, filter.value));
    }

    // Apply ordering
    if (this.config.orderBy) {
      for (const order of this.config.orderBy) {
        q = query(q, orderBy(order.field, order.direction));
      }
    }

    // Apply pagination
    if (this.config.startAfter) {
      q = query(q, startAfter(this.config.startAfter));
    }

    if (this.config.limit) {
      q = query(q, limit(this.config.limit));
    }

    return q;
  }
}

export const createOrderQuery = (filters: OrderFilters = {}) => {
  const builder = QueryBuilder.forCollection(API_CONSTANTS.COLLECTIONS.ORDERS);

  // Apply filters
  if (filters.collectionId) {
    builder.addFilter('collectionId', '==', filters.collectionId);
  }

  // Apply sorting
  if (filters.sortBy && filters.sortBy in SORT_FIELD_MAPPING) {
    const sortConfig = SORT_FIELD_MAPPING[filters.sortBy];
    builder.addOrderBy(sortConfig.field, sortConfig.direction);
  } else {
    builder.addOrderBy(
      API_CONSTANTS.SORT.DEFAULT_FIELD,
      API_CONSTANTS.SORT.DEFAULT_DIRECTION,
    );
  }

  // Apply pagination
  const pageSize = filters.limit ?? API_CONSTANTS.PAGINATION.DEFAULT_PAGE_SIZE;
  builder.setLimit(pageSize + 1); // +1 to check if there are more results

  if (filters.lastDoc) {
    builder.setStartAfter(filters.lastDoc);
  }

  return builder.build();
};

export const createSecondaryMarketQuery = (filters: OrderFilters = {}) => {
  const builder = QueryBuilder.forCollection(API_CONSTANTS.COLLECTIONS.ORDERS)
    .addFilter('status', '==', API_CONSTANTS.FILTERS.SECONDARY_MARKET.STATUS)
    .addFilter(
      'secondaryMarketPrice',
      '>',
      API_CONSTANTS.FILTERS.SECONDARY_MARKET.MIN_PRICE,
    );

  if (filters.collectionId) {
    builder.addFilter('collectionId', '==', filters.collectionId);
  }

  // Apply sorting
  if (filters.sortBy && filters.sortBy in SORT_FIELD_MAPPING) {
    const sortConfig = SORT_FIELD_MAPPING[filters.sortBy];
    builder.addOrderBy(sortConfig.field, sortConfig.direction);
  } else {
    builder.addOrderBy(
      API_CONSTANTS.SORT.DEFAULT_FIELD,
      API_CONSTANTS.SORT.DEFAULT_DIRECTION,
    );
  }

  // Apply pagination
  const pageSize = filters.limit ?? API_CONSTANTS.PAGINATION.DEFAULT_PAGE_SIZE;
  builder.setLimit(pageSize + 1);

  if (filters.lastDoc) {
    builder.setStartAfter(filters.lastDoc);
  }

  return builder.build();
};

export const createUserOrdersQuery = (
  userId: string,
  role: 'buyer' | 'seller',
) => {
  const field = role === 'buyer' ? 'buyerId' : 'sellerId';

  return QueryBuilder.forCollection(API_CONSTANTS.COLLECTIONS.ORDERS)
    .addFilter(field, '==', userId)
    .build();
};

export const createOrderStatsQueries = () => {
  const ordersCollection = API_CONSTANTS.COLLECTIONS.ORDERS;

  return {
    total: QueryBuilder.forCollection(ordersCollection).build(),
    giftSentToRelayer: QueryBuilder.forCollection(ordersCollection)
      .addFilter('status', '==', 'gift_sent_to_relayer')
      .build(),
    fulfilled: QueryBuilder.forCollection(ordersCollection)
      .addFilter('status', '==', 'fulfilled')
      .build(),
    cancelled: QueryBuilder.forCollection(ordersCollection)
      .addFilter('status', '==', 'cancelled')
      .build(),
    paid: QueryBuilder.forCollection(ordersCollection)
      .addFilter('status', '==', 'paid')
      .build(),
  };
};
