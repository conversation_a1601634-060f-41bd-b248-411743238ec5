import type { DocumentSnapshot, QuerySnapshot } from 'firebase/firestore';

import type { OrderEntity } from '@/core.constants';

import { API_CONSTANTS } from './constants';
import type { OrderFilters, PaginatedOrdersResult } from './types';

export interface PaginationConfig {
  pageSize: number;
  maxPageSize?: number;
}

export interface PaginationResult<T> {
  items: T[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
  totalProcessed: number;
}

export const createPaginationConfig = (
  filters: OrderFilters = {},
): PaginationConfig => {
  const pageSize = filters.limit ?? API_CONSTANTS.PAGINATION.DEFAULT_PAGE_SIZE;

  return {
    pageSize: Math.min(pageSize, API_CONSTANTS.PAGINATION.MAX_PAGE_SIZE),
    maxPageSize: API_CONSTANTS.PAGINATION.MAX_PAGE_SIZE,
  };
};

export const processPaginatedSnapshot = <T>(
  snapshot: QuerySnapshot,
  pageSize: number,
  mapFn: (doc: any) => T,
): PaginationResult<T> => {
  const docs = snapshot.docs;
  const items: T[] = [];
  let lastDoc: DocumentSnapshot | null = null;
  let hasMore = false;

  // Check if we have more results than requested
  if (docs.length > pageSize) {
    hasMore = true;
    docs.pop(); // Remove the extra document
  }

  // Process documents
  for (const doc of docs) {
    items.push(mapFn(doc));
    lastDoc = doc;
  }

  return {
    items,
    lastDoc,
    hasMore,
    totalProcessed: items.length,
  };
};

export const createOrderMapper = () => {
  return (doc: any): OrderEntity =>
    ({
      id: doc.id,
      ...doc.data(),
    }) as OrderEntity;
};

export const processPaginatedOrders = (
  snapshot: QuerySnapshot,
  pageSize: number,
): PaginationResult<OrderEntity> => {
  return processPaginatedSnapshot(snapshot, pageSize, createOrderMapper());
};

export const createPaginatedResult = <T>(
  paginationResult: PaginationResult<T>,
): PaginatedOrdersResult => {
  return {
    orders: paginationResult.items as OrderEntity[],
    lastDoc: paginationResult.lastDoc,
    hasMore: paginationResult.hasMore,
  };
};

// For secondary market orders with user filtering
export const processSecondaryMarketPagination = (
  snapshot: QuerySnapshot,
  pageSize: number,
  currentUserId?: string,
): PaginationResult<OrderEntity> => {
  const docs = snapshot.docs;
  const orders: OrderEntity[] = [];
  let lastDoc: DocumentSnapshot | null = null;
  let hasMore = false;
  let processedCount = 0;

  for (const doc of docs) {
    if (processedCount >= pageSize) {
      hasMore = true;
      break;
    }

    const orderData = createOrderMapper()(doc);

    // Apply user filtering if specified
    if (currentUserId) {
      if (
        orderData.buyerId === currentUserId ||
        orderData.sellerId === currentUserId
      ) {
        orders.push(orderData);
        lastDoc = doc;
        processedCount++;
      }
    } else {
      orders.push(orderData);
      lastDoc = doc;
      processedCount++;
    }
  }

  return {
    items: orders,
    lastDoc,
    hasMore,
    totalProcessed: processedCount,
  };
};

// Utility to validate pagination parameters
export const validatePaginationParams = (
  filters: OrderFilters,
): OrderFilters => {
  const validated = { ...filters };

  if (validated.limit) {
    validated.limit = Math.max(
      1,
      Math.min(validated.limit, API_CONSTANTS.PAGINATION.MAX_PAGE_SIZE),
    );
  }

  return validated;
};

// Create empty paginated result
export const createEmptyPaginatedResult = (): PaginatedOrdersResult => ({
  orders: [],
  lastDoc: null,
  hasMore: false,
});

// Merge multiple paginated results (useful for combining buyer/seller orders)
export const mergePaginatedResults = (
  results: PaginatedOrdersResult[],
): PaginatedOrdersResult => {
  const allOrders = results.flatMap((result) => result.orders);
  const hasMore = results.some((result) => result.hasMore);

  // Use the last document from the last result that has one
  const lastDoc =
    results.reverse().find((result) => result.lastDoc)?.lastDoc || null;

  return {
    orders: allOrders,
    lastDoc,
    hasMore,
  };
};
