import { doc, getDoc, setDoc } from 'firebase/firestore';

import {
  APP_CONFIG_COLLECTION,
  APP_CONFIG_DOC_ID,
  CACHE_CONFIG,
} from '@/core.constants';
import { firestore } from '@/root-context';
import { globalCache } from '@/utils/cache-utils';

export interface AppConfig {
  id: string;
  purchase_fee: number; // in BPS
  reject_order_fee: number; // in BPS
  referrer_fee: number; // in BPS
  min_deposit_amount: number; // in TON
  min_withdrawal_amount: number; // in TON
  max_withdrawal_amount: number; // in TON
  buyer_lock_percentage: number; // Percentage of order amount locked by buyer (0.0-1.0)
  seller_lock_percentage: number; // Percentage of order amount locked by seller (0.0-1.0)
  deposit_fee: number; // in TON
  withdrawal_fee: number; // in TON
  min_secondary_market_price: number; // in TON
}

export const getAppConfig = async (): Promise<AppConfig | null> => {
  const cachedConfig = globalCache.getAppConfig<AppConfig>();
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    const configRef = doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID);
    const configDoc = await getDoc(configRef);

    if (!configDoc.exists()) {
      return null;
    }

    const config = {
      id: configDoc.id,
      ...configDoc.data(),
    } as AppConfig;

    globalCache.setAppConfig(config, CACHE_CONFIG);

    return config;
  } catch (error) {
    console.error('Error getting app config:', error);
    throw error;
  }
};

export const updateAppConfig = async (updates: Partial<AppConfig>) => {
  try {
    const configRef = doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID);

    await setDoc(configRef, updates, { merge: true });

    clearAppConfigCache();

    console.log('App config updated successfully:', updates);
  } catch (error) {
    console.error('Error updating app config:', error);
    throw error;
  }
};

export const clearAppConfigCache = () => {
  globalCache.invalidate('app_config:main');
};
