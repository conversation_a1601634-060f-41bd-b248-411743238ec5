import { getDocs } from 'firebase/firestore';

import type { OrderEntity } from '@/core.constants';

import { getCurrentUserId } from './auth-api';
import {
  API_CONSTANTS,
  cloudFunctionCaller,
  createPaginatedResult,
  createPaginationConfig,
  ERROR_MESSAGES,
  type OrderFilters,
  type PaginatedOrdersResult,
  processPaginatedOrders,
  QueryBuilder,
} from './shared';

export interface MakePurchaseAsBuyerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const getOrdersForBuyers = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const paginationConfig = createPaginationConfig(filters);

    // Create query for active orders with sellers (available for buyers)
    const query = QueryBuilder.forCollection(API_CONSTANTS.COLLECTIONS.ORDERS)
      .addFilter('status', '==', 'active')
      .addFilter('sellerId', '!=', null);

    // Add collection filter if specified
    if (filters.collectionId) {
      query.addFilter('collectionId', '==', filters.collectionId);
    }

    // Add sorting - use 'amount' field for price sorting in buyer orders
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'price_asc':
          query.addOrderBy('amount', 'asc');
          break;
        case 'price_desc':
          query.addOrderBy('amount', 'desc');
          break;
        case 'date_asc':
          query.addOrderBy('createdAt', 'asc');
          break;
        case 'date_desc':
          query.addOrderBy('createdAt', 'desc');
          break;
      }
    } else {
      query.addOrderBy('createdAt', 'desc');
    }

    // Add pagination
    if (filters.lastDoc) {
      query.setStartAfter(filters.lastDoc);
    }
    query.setLimit(paginationConfig.pageSize + 1);

    const snapshot = await getDocs(query.build());

    // Process pagination and filter for buyers (orders without buyerId)
    const paginationResult = processPaginatedOrders(
      snapshot,
      paginationConfig.pageSize,
    );

    // Filter orders available for buyers (no buyerId and not created by current user)
    let filteredOrders = paginationResult.items.filter(
      (order) =>
        !order.buyerId &&
        (!filters.currentUserId || order.sellerId !== filters.currentUserId),
    );

    // Apply client-side price filtering using amount field
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      const priceFilter = (order: OrderEntity) => {
        if (filters.minPrice !== undefined && order.amount < filters.minPrice) {
          return false;
        }
        if (filters.maxPrice !== undefined && order.amount > filters.maxPrice) {
          return false;
        }
        return true;
      };
      filteredOrders = filteredOrders.filter(priceFilter);
    }

    return createPaginatedResult({
      ...paginationResult,
      items: filteredOrders,
    });
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_ORDERS, error);
    throw error;
  }
};

export const makePurchaseAsBuyer = async (
  orderId: string,
): Promise<MakePurchaseAsBuyerResponse> => {
  try {
    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await cloudFunctionCaller.makePurchaseAsBuyer({
      buyerId: currentUserId,
      orderId,
    });

    return result;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};
