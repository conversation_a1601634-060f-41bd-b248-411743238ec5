import { getDocs } from 'firebase/firestore';
import type { OrderEntity } from '@/core.constants';
import {
  type OrderFilters,
  type PaginatedOrdersResult,
  QueryBuilder,
  createPaginationConfig,
  processPaginatedOrders,
  createPaginatedResult,
  cloudFunctionCaller,
  API_CONSTANTS,
  ERROR_MESSAGES,
} from './shared';

export interface MakePurchaseAsSellerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const getOrdersForSellers = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const paginationConfig = createPaginationConfig(filters);

    // Create query for active orders with buyers (available for sellers)
    const query = QueryBuilder.forCollection(API_CONSTANTS.COLLECTIONS.ORDERS)
      .addFilter('status', '==', 'active')
      .addFilter('buyerId', '!=', null);

    // Add collection filter if specified
    if (filters.collectionId) {
      query.addFilter('collectionId', '==', filters.collectionId);
    }

    // Add sorting - use 'amount' field for price sorting in seller orders
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'price_asc':
          query.addOrderBy('amount', 'asc');
          break;
        case 'price_desc':
          query.addOrderBy('amount', 'desc');
          break;
        case 'date_asc':
          query.addOrderBy('createdAt', 'asc');
          break;
        case 'date_desc':
          query.addOrderBy('createdAt', 'desc');
          break;
      }
    } else {
      query.addOrderBy('createdAt', 'desc');
    }

    // Add pagination
    if (filters.lastDoc) {
      query.setStartAfter(filters.lastDoc);
    }
    query.setLimit(paginationConfig.pageSize + 1);

    const snapshot = await getDocs(query.build());

    // Process pagination and filter for sellers (orders without sellerId)
    const paginationResult = processPaginatedOrders(
      snapshot,
      paginationConfig.pageSize,
    );

    // Filter orders available for sellers (no sellerId and not created by current user)
    let filteredOrders = paginationResult.items.filter(
      (order) =>
        !order.sellerId &&
        (!filters.currentUserId || order.buyerId !== filters.currentUserId),
    );

    // Apply client-side price filtering using amount field
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      const priceFilter = (order: OrderEntity) => {
        if (filters.minPrice !== undefined && order.amount < filters.minPrice) {
          return false;
        }
        if (filters.maxPrice !== undefined && order.amount > filters.maxPrice) {
          return false;
        }
        return true;
      };
      filteredOrders = filteredOrders.filter(priceFilter);
    }

    return createPaginatedResult({
      ...paginationResult,
      items: filteredOrders,
    });
  } catch (error) {
    console.error(ERROR_MESSAGES.FETCH_ORDERS, error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (orderId: string) => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseAsSellerResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsSeller);

    const auth = await import('firebase/auth');
    const currentUser = auth.getAuth().currentUser;

    if (!currentUser) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUser.uid,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};
