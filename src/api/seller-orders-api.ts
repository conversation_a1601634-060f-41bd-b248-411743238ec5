import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  AppCloudFunctions,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
} from '@/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseAsSellerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const getOrdersForSellers = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('status', '==', 'active'),
      where('buyerId', '!=', null),
    );

    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy('amount', 'asc'));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy('amount', 'desc'));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);

    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (const element of docs) {
      const doc = element;

      if (processedCount >= pageSize) {
        hasMore = true;
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      if (
        !orderData.sellerId &&
        (!filters.currentUserId || orderData.buyerId !== filters.currentUserId)
      ) {
        orders.push(orderData);
        lastDoc = doc;
        processedCount++;
      }
    }

    if (filters.minPrice !== undefined) {
      orders = orders.filter((order) => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter((order) => order.amount <= filters.maxPrice!);
    }

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    return result;
  } catch (error) {
    console.error('Error fetching orders for sellers:', error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (orderId: string) => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseAsSellerResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsSeller);

    const auth = await import('firebase/auth');
    const currentUser = auth.getAuth().currentUser;

    if (!currentUser) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUser.uid,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};
